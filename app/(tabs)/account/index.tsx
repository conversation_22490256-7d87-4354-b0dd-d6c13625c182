import { LinkList, LinkListItem } from "@/components/LinkList";
import { StyleSheet, View } from "react-native";
import { LegalAndPrivacy } from "./_data";

export default function AccountScreen() {
  return (
    <View style={styles.container}>
      <LinkList>
        {LegalAndPrivacy.content.map((item, index) => (
          <LinkListItem
            key={`legal-setting-${item.title}-${index}`}
            {...item}
          />
        ))}
      </LinkList>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
});
