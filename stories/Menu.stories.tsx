import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { NavigationContainer } from "@react-navigation/native";
import React from "react";
import { Platform, StyleSheet, Text, View } from "react-native";

// App UI pieces reused from your tabs layout
import TabB<PERSON>Background from "@/components/ui/TabBarBackground";
import {
  AccountIcon,
  CompasIcon,
  Icon,
  OrganizerIcon,
} from "@/components/ui/icon";

const Tab = createBottomTabNavigator();

// Simple placeholder screens for Storybook preview
function ExploreScreen() {
  return (
    <View style={styles.screenContainer}>
      <Text>Explore</Text>
    </View>
  );
}

function OrganizerScreen() {
  return (
    <View style={styles.screenContainer}>
      <Text>Organizer</Text>
    </View>
  );
}

function AccountScreen() {
  return (
    <View style={styles.screenContainer}>
      <Text>Account</Text>
    </View>
  );
}

// This mirrors your app/(tabs)/_layout.tsx configuration using react-navigation directly
function TabsMenuPreview() {
  return (
    <NavigationContainer>
      <Tab.Navigator
        initialRouteName="explore"
        screenOptions={{
          headerShown: false,
          tabBarBackground: TabBarBackground,
          tabBarActiveTintColor: "black",
          tabBarStyle: Platform.select({
            ios: {
              // Use a transparent background on iOS to show the blur effect
              position: "absolute",
            },
            default: {},
          }),
        }}
      >
        <Tab.Screen
          name="explore"
          component={ExploreScreen}
          options={{
            title: "Explore",
            tabBarIcon: ({ color }) => (
              <Icon as={CompasIcon} size="xl" fill={color} stroke={color} />
            ),
          }}
        />
        <Tab.Screen
          name="organizer"
          component={OrganizerScreen}
          options={{
            title: "Organizer",
            tabBarIcon: ({ color }) => (
              <Icon as={OrganizerIcon} size="xl" fill={color} stroke={color} />
            ),
          }}
        />
        <Tab.Screen
          name="account"
          component={AccountScreen}
          options={{
            title: "Account",
            tabBarIcon: ({ color }) => (
              <Icon as={AccountIcon} size="xl" fill={color} stroke={color} />
            ),
          }}
        />
      </Tab.Navigator>
    </NavigationContainer>
  );
}

export default {
  title: "Navigation/BottomBar",
  component: TabsMenuPreview,
};

export const Default = () => <TabsMenuPreview />;

const styles = StyleSheet.create({
  screenContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
});
