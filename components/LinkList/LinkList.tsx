import React, { Fragment } from "react";
import { Box } from "../ui/box";
import { Divider } from "../ui/divider";

interface Props {
  children: React.ReactNode;
}

export const LinkList = ({ children }: Props) => {
  const items = React.Children.toArray(children);
  return (
    <Box className="w-full rounded-xl overflow-hidden border border-gray-300">
      {items.map((child, idx) => (
        <Fragment key={idx}>
          {child}
          {idx < items.length - 1 ? <Divider /> : null}
        </Fragment>
      ))}
    </Box>
  );
};
